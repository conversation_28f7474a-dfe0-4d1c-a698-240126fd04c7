import type React from 'react';
import {
  SettingOutlined,
  MoreOutlined,
  CopyOutlined,
  EditOutlined,
  DeleteOutlined,
  Bar<PERSON>hartOutlined,
  TableOutlined,
  ExpandOutlined,
} from '@ant-design/icons';
import { useState, useEffect, useRef } from 'react';
import { Table, Dropdown, Menu, Modal, Input, message, Tooltip, Popover, Typography } from 'antd';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkBreaks from 'remark-breaks';
import rehypeRaw from 'rehype-raw';
import FullScreenChartModal from '../FullScreenChartModal/index';
import ConfigModal from '../ConfigModal';
import { editChart, deleteChart } from '@/services/DataLoom/yibiaopanjiekou';
import { extractTableFromOption } from '@/utils/echartsToTable';
import './index.less';

interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  showFullScreenLink?: boolean;
  data?: any[]; // 表格数据
  columns?: any[]; // 表格列定义
  chartId: string; // 添加chartId属性
  dashboardId: string; // 添加dashboardId属性
  chartName: string; // 添加chartName属性
  onRename?: (newTitle: string) => void;
  onDelete?: () => void;
  onAnalyze?: () => void; // 添加智能分析回调
  chartOption?: any; // 添加图表配置选项
  analysisRes?: string; // 添加智能分析结果
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  children,
  showFullScreenLink = true,
  data = [],
  columns = [],
  chartId,
  dashboardId,
  chartName,
  onRename,
  onDelete,
  onAnalyze,
  chartOption,
  analysisRes,
}) => {
  const [showChart, setShowChart] = useState(true);
  const [isFullScreenVisible, setIsFullScreenVisible] = useState(false);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newTitle, setNewTitle] = useState(title);
  const [tableData, setTableData] = useState<{ columns: any[]; dataSource: any[] }>({ columns: [], dataSource: [] });
  const [isHovered, setIsHovered] = useState(false);
  const [showAnalysisPopover, setShowAnalysisPopover] = useState(false);
  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);

  useEffect(() => {
    if (chartOption) {
      const { columns: extractedColumns, dataSource } = extractTableFromOption(chartOption);
      setTableData({
        columns: extractedColumns,
        dataSource: dataSource,
      });
    }
  }, [chartOption]);

  const showFullScreen = () => {
    setIsFullScreenVisible(true);
  };

  const hideFullScreen = () => {
    setIsFullScreenVisible(false);
  };

  const toggleChart = () => {
    setShowChart(!showChart);
  };

  const handleRename = async () => {
    try {
      const params = {
        dashboardId: dashboardId,
        chartName: newTitle,
        id: chartId,
      };
      const res = await editChart(params);
      if (res.code === 0) {
        if (onRename) {
          onRename(newTitle);
        }
        message.success('重命名成功');
        setIsRenameModalVisible(false);
      } else {
        message.error(res.message || '重命名失败');
      }
    } catch (error) {
      message.error('重命名失败');
    }
  };

  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个图表吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const params = {
            chartId: chartId,
          };
          await deleteChart(params);
          if (onDelete) {
            onDelete();
          }
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  const handleCopy = () => {
    // 复制图表功能
    message.info('复制图表功能开发中...');
  };

  const handleCopyTo = () => {
    // 复制到其他仪表盘功能
    message.info('复制到其他仪表盘功能开发中...');
  };

  const handleConfig = () => {
    setIsConfigModalVisible(true);
  };

  const handleConfigConfirm = (config: any) => {
    console.log('图表配置结果:', config);
    setIsConfigModalVisible(false);
    message.success('配置保存成功');
    // 这里可以添加保存配置到后端的逻辑
  };

  const handleConfigCancel = () => {
    setIsConfigModalVisible(false);
  };

  const handleAnalyzeClick = () => {
    setShowAnalysisPopover(true);

    // 调用原有的 onAnalyze 回调
    if (onAnalyze) {
      onAnalyze();
    }
  };

  // 悬浮菜单项配置
  const hoverMenuItems = [
    {
      key: 'config',
      icon: <SettingOutlined />,
      label: '配置',
      onClick: handleConfig,
      show: true,
    },
    {
      key: 'table',
      icon: showChart ? <TableOutlined /> : <BarChartOutlined />,
      label: showChart ? '查看表格' : '查看图表',
      onClick: toggleChart,
      show: true,
    },
    {
      key: 'fullscreen',
      icon: <ExpandOutlined />,
      label: '查看大图',
      onClick: showFullScreen,
      show: showFullScreenLink,
    },
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: '复制',
      onClick: handleCopy,
      show: true,
    },
    {
      key: 'copyTo',
      icon: <CopyOutlined />,
      label: '复制到',
      onClick: handleCopyTo,
      show: true,
    },
    {
      key: 'rename',
      icon: <EditOutlined />,
      label: '重命名',
      onClick: () => setIsRenameModalVisible(true),
      show: true,
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: '删除',
      onClick: handleDelete,
      show: true,
      danger: true,
    },
  ];

  // 创建智能分析内容
  const { Title } = Typography;

  const analysisContent = analysisRes ? (
    <div className="analysis-content">
      {/* 标题部分 */}
      <div className="analysis-header">
        <div className="analysis-icon">
          <img src="/assets/image_1753770268615_99iqw9.png" alt="智能分析" />
        </div>
        <Title level={4} className="analysis-title">
          智能分析
        </Title>
      </div>

      {/* 智能分析结果 */}
      <div className="common-paragraph paragraph-body">
        <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks]} rehypePlugins={[rehypeRaw]}>
          {analysisRes}
        </ReactMarkdown>
      </div>
    </div>
  ) : null;

  return (
    <div className="chart-container" onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <div className="chart-header">
        <Tooltip title={title}>
          <span className="chart-title">{title}</span>
        </Tooltip>
        <div className="chart-actions">
          {isHovered && analysisRes && (
            <Popover
              content={analysisContent}
              title={null}
              trigger="click"
              open={showAnalysisPopover}
              onOpenChange={setShowAnalysisPopover}
              placement="bottomLeft"
            >
              <span className="action-link" onClick={handleAnalyzeClick}>
                智能分析
              </span>
            </Popover>
          )}
          {/* 悬浮菜单 */}
          {isHovered && (
            <div className="hover-menu">
              <Dropdown
                menu={{
                  items: hoverMenuItems
                    .filter((item) => item.show)
                    .map((item) => ({
                      key: item.key,
                      icon: item.icon,
                      label: item.label,
                      onClick: item.onClick,
                      danger: item.danger,
                    })),
                }}
                trigger={['click']}
                placement="bottomLeft"
              >
                <div className="hover-menu-trigger">
                  <img src="/assets/image_1753770979113_p4gw6n.svg" alt="更多" />
                </div>
              </Dropdown>
            </div>
          )}
        </div>
      </div>
      <div className="chart-content">
        {showChart ? (
          children
        ) : (
          <Table
            dataSource={tableData.dataSource}
            columns={tableData.columns}
            pagination={false}
            size="small"
            scroll={{ y: 200 }}
            bordered
          />
        )}
      </div>

      <FullScreenChartModal
        visible={isFullScreenVisible}
        onClose={hideFullScreen}
        title={title}
        width="80%"
        height="85vh"
        showSettings={true}
        initialMode={showChart ? 'chart' : 'table'}
        chartOption={chartOption}
        onRename={() => setIsRenameModalVisible(true)}
        onDelete={() => handleDelete()}
      >
        {children}
      </FullScreenChartModal>

      <Modal
        title="重命名图表"
        className="rename-chart-modal common-modal"
        zIndex={9999} //最大层级
        open={isRenameModalVisible}
        onOk={handleRename}
        onCancel={() => setIsRenameModalVisible(false)}
      >
        <Input value={newTitle} onChange={(e) => setNewTitle(e.target.value)} placeholder="请输入新的图表名称" />
      </Modal>

      <ConfigModal visible={isConfigModalVisible} onCancel={handleConfigCancel} onConfirm={handleConfigConfirm} />
    </div>
  );
};

export default ChartContainer;
